<script setup lang="tsx">
import { defineComponent, h, onMounted, reactive, ref, createApp } from 'vue'
import Dialog from './components/popup/Dialog.vue';
import { RsaEncrypt } from './utils/RSAUtil'
let form: any = reactive({})
let loading = ref(false)
let show:any = ref(false)

onMounted(() => {

  setInterval(() => {
    let $popover = document.querySelector('.ant-popover.user-popover:not([style*="display: none"])')
    if (!$popover) return

    let $userinfo = $popover.querySelector('.author-card .userInfo-right-cn')

    let $acc: any = $userinfo?.querySelector('#account_change')
    if ($acc) return

    $acc = createElement(`<a id="account_change" href="javascript:void(0);">账号切换</a>`)
    $acc.onclick = function () {
      let $account = $userinfo?.querySelector('span')
      form.account = (($account?.innerHTML) + '').trim()
      onSubmit()
    }

    $userinfo?.appendChild($acc)

  }, 1000)
  setTimeout(() => {
    let { href } = location
    if (href.startsWith(`${location.origin}/login`)) {
      show.value = true
    }
  }, 1000)


})

function createElement(str: any) {
  let $div = document.createElement('div')
  $div.innerHTML = str
  return $div.firstChild
}

async function onSubmit() {
  try {
    loading.value = true
    let public_key = await getKey()
    let auth_code = await getCode(public_key)

    let token = await getToken(auth_code.code)

    localStorage.setItem('token', token)


    console.log('location.href', location.href)
    if (location.href.startsWith(`${location.origin}/login`)) {
      location.href = `${location.origin}/`
    } else {

      location.reload()
    }

  } catch (error) {
    loading.value = false
  }

}
async function getKey() {
  let resp = await fetch('/api/public/getKey')
  return await resp.json()
}
async function getCode(public_key: any) {
  let { index, key } = await public_key
  form.password = 'Authine@123456'
  //if (form.account == 'ye') form.password = '123456'
  let password = RsaEncrypt(form.password, key)
  let redirect_uri = `${location.origin}/api/oauth/authorize?client_id=api&response_type=code&scope=read&redirect_uri=${location.origin}/oauth`
  let url = `${location.origin}/api/login?redirect_uri=${encodeURIComponent(`${redirect_uri}`)}`
  let corpId = localStorage.getItem('corpId')
  let resp = await fetch('/api/login/Authentication/get_code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      "username": form.account,
      password,
      url,
      "portal": true, "index": index,
      "corpId": corpId
    })
  })

  return await resp.json()
}
async function getToken(code: any) {
  let resp: any = await fetch(`${location.origin}/api/login/Authentication/get_token?code=${code}&url=${encodeURIComponent(`${location.origin}/api`)}&client_secret=c31b32364ce19ca8fcd150a417ecce58&client_id=api&redirect_uri=${encodeURIComponent(`${location.origin}/oauth`)}`)
  let body = await resp.json()
  return body.access_token
}

function handleClickDefault() {
  form.account = '***********'
  onSubmit()
}

</script>

<template>
  <div class="change-btn" @click="show = true">
    <div class="change-btn-logo">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img"
        class="iconify iconify--logos" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 257">
        <defs>
          <linearGradient id="IconifyId1813088fe1fbc01fb466" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%">
            <stop offset="0%" stop-color="#41D1FF"></stop>
            <stop offset="100%" stop-color="#BD34FE"></stop>
          </linearGradient>
          <linearGradient id="IconifyId1813088fe1fbc01fb467" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%">
            <stop offset="0%" stop-color="#FFEA83"></stop>
            <stop offset="8.333%" stop-color="#FFDD35"></stop>
            <stop offset="100%" stop-color="#FFA800"></stop>
          </linearGradient>
        </defs>
        <path fill="url(#IconifyId1813088fe1fbc01fb466)"
          d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.537 6.537 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62Z">
        </path>
        <path fill="url(#IconifyId1813088fe1fbc01fb467)"
          d="M185.432.063L96.44 17.501a3.268 3.268 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z">
        </path>
      </svg>
    </div>
    <Dialog v-model="show" title="账号切换" message="请输入要切换的账号" @confirm="onSubmit" :width="300" :height="200">
      <div style="display:flex;width:100%">
        <label style="margin-right:10px">账号</label>
        <input style="flex:1" v-model="form.account"></input>
      </div>
      <div style="margin-top:10px">
        <a style="color: red;" @click="handleClickDefault">默认账号</a>
      </div>

      <div style="margin-top:10px;display:flex;width:100%">
        <label style="margin-right:10px">Token</label>
        <input style="flex:1" v-model="form.token"></input>
      </div>
      <div style="margin-top:10px">
        <a style="color: #666;" @click="() => {
          if(form.token) {
            localStorage.setItem('token', form.token);
            location.href = '/';
          }
        }">使用Token登录</a>
      </div>
      <div style="margin-top:10px">
        <a style="color: #666;word-break:break-all;" @click="() => {
          navigator.clipboard.writeText(localStorage.getItem('token') || '');
        }">{{ localStorage.getItem('token') || '暂无token' }}</a>
      </div>

    </Dialog>
  </div>
</template>

<style scoped lang="scss">
.change-btn {
  border: 1px solid #555;
  border-radius: 50px;
  position: fixed;
  top: 50%;
  left: -20px;
  z-index: 10000;
  padding: 5px;
  cursor: pointer;
  transition: all .5s;
  line-height: 0;
  //opacity: 0.5;
  color: #555555;
  background-color: #efefef;

  &-logo {
    height: 15px;
    width: 15px;
    will-change: filter;
  }

  &:hover {
    left: 0px;
  }
}
</style>

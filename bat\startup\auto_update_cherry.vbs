' 设置常量
Const CHERRY_RELEASES_URL = "https://github.com/CherryHQ/cherry-studio/releases"
Const TARGET_DIR = "D:\Software\Other\Cherry"
Const MAX_RETRIES = 3 ' 最大重试次数
Const RETRY_DELAY = 2000 ' 重试延迟(毫秒)

' 创建对象
Set objShell = CreateObject("WScript.Shell")

Set objFSO = CreateObject("Scripting.FileSystemObject")
Set objXMLHTTP = CreateObject("MSXML2.ServerXMLHTTP.6.0")


' 获取发布页面内容 - 添加重试逻辑
Dim retryCount
retryCount = 0
Do
    On Error Resume Next
    objXMLHTTP.Open "GET", CHERRY_RELEASES_URL, False
    objXMLHTTP.setRequestHeader "User-Agent", "Mozilla/5.0"
    objXMLHTTP.Send

    If Err.Number = 0 And objXMLHTTP.Status = 200 Then
        Exit Do
    End If

    retryCount = retryCount + 1
    If retryCount < MAX_RETRIES Then
        WScript.Echo "get release page failed, " & (MAX_RETRIES - retryCount) & " times retry..."
        WScript.Sleep RETRY_DELAY
    End If
Loop While retryCount < MAX_RETRIES


On Error Goto 0

If objXMLHTTP.Status <> 200 Then
    WScript.Echo "get release page failed: " & objXMLHTTP.Status
    WScript.Quit 1
End If


' 读取当前版本号
versionFilePath = ".\cherry.version"
currentVersion = ""
If objFSO.FileExists(versionFilePath) Then
    Set versionFile = objFSO.OpenTextFile(versionFilePath, 1)
    currentVersion = versionFile.ReadLine()
    versionFile.Close
End If

' 全局变量
availableVersion = ""
downloadUrl = ""

If objXMLHTTP.Status = 200 Then
    content = objXMLHTTP.responseText

    ' 使用正则表达式匹配所有版本号
    Set regex = New RegExp
    regex.Pattern = "v([0-9]+\.[0-9]+\.[0-9]+)"
    regex.Global = True

    Set matches = regex.Execute(content)
    If matches.Count > 0 Then
        WScript.Echo "found " & matches.Count & " versions, checking download availability..."

        ' 遍历所有版本，从最新开始检查
        For i = 0 To matches.Count - 1
            testVersion = matches(i).SubMatches(0)
            testDownloadUrl = "https://github.com/CherryHQ/cherry-studio/releases/download/v" & testVersion & "/Cherry-Studio-" & testVersion & "-x64-portable.exe"

            WScript.Echo "checking version v" & testVersion & "..."

            ' 检查下载链接是否可访问
            If CheckDownloadAvailable(testDownloadUrl) Then
                WScript.Echo "version v" & testVersion & " download link is available"

                ' 如果当前版本为空或者找到的版本比当前版本新，则使用这个版本
                If currentVersion = "" Or CompareVersions(testVersion, currentVersion) > 0 Then
                    availableVersion = testVersion
                    downloadUrl = testDownloadUrl
                    WScript.Echo "found newer available version: v" & availableVersion
                    Exit For
                ElseIf currentVersion = testVersion Then
                    WScript.Echo "current version v" & currentVersion & " is up to date"
                    WScript.Quit 0
                Else
                    WScript.Echo "version v" & testVersion & " is older than current version v" & currentVersion
                End If
            Else
                WScript.Echo "version v" & testVersion & " download link is not available, trying next version..."
            End If
        Next

    Else
        WScript.Echo "can't find version number"
        WScript.Quit 1
    End If
Else
    WScript.Echo "get release page failed: " & objXMLHTTP.Status
    WScript.Quit 1
End If

' 检查是否找到可用的更新版本
If availableVersion = "" Then
    WScript.Echo "no available update found"
    WScript.Quit 0
End If

WScript.Echo "will download version: v" & availableVersion
WScript.Echo "download link: " & downloadUrl

' 下载文件 - 添加等待和验证逻辑
Set objStream = CreateObject("ADODB.Stream")
retryCount = 0

Do
    On Error Resume Next
    objXMLHTTP.Open "GET", downloadUrl, False
    objXMLHTTP.setTimeouts 5000, 60000, 60000, 120000
    objXMLHTTP.setRequestHeader "User-Agent", "Mozilla/5.0"
    objXMLHTTP.setRequestHeader "Accept", "*/*"

    WScript.Echo "start to download file..."
    objXMLHTTP.Send


    If Err.Number = 0 And objXMLHTTP.Status = 200 And objXMLHTTP.readyState = 4 Then
        WScript.Echo "download finished!"
        Exit Do
    End If


    retryCount = retryCount + 1
    If retryCount < MAX_RETRIES Then
        WScript.Echo "download file failed, " & (MAX_RETRIES - retryCount) & " times retry..."
        WScript.Sleep RETRY_DELAY
    End If

Loop While retryCount < MAX_RETRIES

On Error Goto 0

If objXMLHTTP.Status <> 200 Then
    WScript.Echo "download file failed: " & objXMLHTTP.Status
    WScript.Quit 1
End If


' 确保响应数据完整后再保存
If IsNull(objXMLHTTP.responseBody) Then
    WScript.Echo "download data is empty, please retry"
    WScript.Quit 1
End If


' 保存文件
On Error Resume Next
objStream.Open
objStream.Type = 1
objStream.Write objXMLHTTP.responseBody
objStream.SaveToFile "cherry_portable.exe", 2
If Err.Number <> 0 Then
    WScript.Echo "save file failed: " & Err.Description
    WScript.Quit 1
End If
objStream.Close
WScript.Echo "file saved!"
On Error Goto 0


' 提取7z文件 - 使用 e 命令提取单个文件到当前目录，不保留路径结构
objShell.Run """D:\Software\Tool\7-Zip\7z.exe"" e cherry_portable.exe $PLUGINSDIR\app-64.7z -aoa", 0, True
' 清空目标目录
If objFSO.FolderExists(TARGET_DIR) Then
objFSO.DeleteFolder TARGET_DIR
End If
objFSO.CreateFolder TARGET_DIR
' 解压到目标目录
objShell.Run """D:\Software\Tool\7-Zip\7z.exe"" x app-64.7z -o""" & TARGET_DIR & """", 0, True
' 清理临时文件
objFSO.DeleteFile "cherry_portable.exe"
objFSO.DeleteFile "app-64.7z"

' 写入最新版本号
Set versionFile = objFSO.CreateTextFile(versionFilePath, True)
versionFile.WriteLine availableVersion
versionFile.Close

' 检查下载链接是否可用的函数
Function CheckDownloadAvailable(url)
    On Error Resume Next
    Set testXMLHTTP = CreateObject("MSXML2.ServerXMLHTTP.6.0")
    testXMLHTTP.Open "HEAD", url, False
    testXMLHTTP.setRequestHeader "User-Agent", "Mozilla/5.0"
    testXMLHTTP.setTimeouts 5000, 10000, 10000, 10000
    testXMLHTTP.Send

    If Err.Number = 0 And testXMLHTTP.Status = 200 Then
        CheckDownloadAvailable = True
    Else
        CheckDownloadAvailable = False
    End If

    Set testXMLHTTP = Nothing
    On Error Goto 0
End Function

' 比较版本号的函数 (返回 1 表示 version1 > version2, -1 表示 version1 < version2, 0 表示相等)
Function CompareVersions(version1, version2)
    Dim v1Parts, v2Parts
    v1Parts = Split(version1, ".")
    v2Parts = Split(version2, ".")

    Dim i, v1Num, v2Num
    For i = 0 To 2
        v1Num = CInt(v1Parts(i))
        v2Num = CInt(v2Parts(i))

        If v1Num > v2Num Then
            CompareVersions = 1
            Exit Function
        ElseIf v1Num < v2Num Then
            CompareVersions = -1
            Exit Function
        End If
    Next

    CompareVersions = 0
End Function